@extends('layouts.app')

@section('title', 'IELTS Writing Scorer - Professional AI Assessment')

@section('content')
<style>
    :root {
        --primary: #667eea;
        --secondary: #764ba2;
        --success: #27ae60;
        --danger: #e74c3c;
        --warning: #f39c12;
        --info: #3498db;
        --dark: #2c3e50;
        --light: #ecf0f1;
        --gradient-primary: linear-gradient(135deg, var(--primary), var(--secondary));
        --gradient-success: linear-gradient(135deg, #11998e, #38ef7d);
        --gradient-danger: linear-gradient(135deg, #ff6b6b, #ee5a24);
        --gradient-warning: linear-gradient(135deg, #f093fb, #f5576c);
        --gradient-info: linear-gradient(135deg, #4facfe, #00f2fe);
        --shadow-soft: 0 10px 40px rgba(0,0,0,0.1);
        --shadow-medium: 0 15px 50px rgba(0,0,0,0.15);
        --shadow-strong: 0 20px 60px rgba(0,0,0,0.2);
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', sans-serif;
        background: var(--gradient-primary);
        min-height: 100vh;
        color: var(--dark);
        overflow-x: hidden;
    }

    .main-container {
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        border-radius: 24px;
        box-shadow: var(--shadow-strong);
        margin: 20px auto;
        max-width: 1600px;
        overflow: hidden;
        position: relative;
    }

    .header-section {
        background: var(--gradient-primary);
        color: white;
        padding: 60px 40px;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .header-section::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .header-content {
        position: relative;
        z-index: 2;
    }

    .header-title {
        font-size: 3.5rem;
        font-weight: 800;
        margin-bottom: 20px;
        text-shadow: 0 4px 8px rgba(0,0,0,0.2);
        letter-spacing: -1px;
    }

    .header-subtitle {
        font-size: 1.3rem;
        opacity: 0.95;
        font-weight: 400;
        max-width: 600px;
        margin: 0 auto;
    }

    .content-section {
        padding: 50px;
    }

    /* Score Dashboard */
    .score-dashboard {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 40px;
        margin-bottom: 50px;
    }

    .overall-score-card {
        background: var(--gradient-primary);
        color: white;
        padding: 50px 30px;
        border-radius: 24px;
        text-align: center;
        box-shadow: var(--shadow-medium);
        position: relative;
        overflow: hidden;
    }

    .overall-score-card::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: rotate 30s linear infinite;
    }

    .score-number {
        font-size: 5rem;
        font-weight: 800;
        margin-bottom: 15px;
        text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        position: relative;
        z-index: 2;
    }

    .score-label {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 10px;
        position: relative;
        z-index: 2;
    }

    .score-description {
        font-size: 1rem;
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }

    .criteria-breakdown {
        background: white;
        border-radius: 24px;
        padding: 35px;
        box-shadow: var(--shadow-soft);
    }

    .criteria-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--dark);
        margin-bottom: 25px;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .criteria-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .criteria-item {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 16px;
        padding: 25px;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid transparent;
    }

    .criteria-item:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
        border-color: var(--primary);
    }

    .criteria-name {
        font-weight: 600;
        color: var(--dark);
        margin-bottom: 10px;
        font-size: 0.9rem;
    }

    .criteria-score {
        font-size: 2.5rem;
        font-weight: 800;
        color: var(--primary);
        margin-bottom: 8px;
    }

    .criteria-feedback {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 10px;
    }

    .criteria-action {
        font-size: 0.8rem;
        color: var(--primary);
        font-weight: 500;
    }

    /* Analysis Section */
    .analysis-section {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 40px;
        margin-bottom: 50px;
    }

    .essay-analysis, .corrections-panel {
        background: white;
        border-radius: 24px;
        padding: 35px;
        box-shadow: var(--shadow-soft);
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
    }

    .section-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: var(--dark);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .view-toggle {
        display: flex;
        gap: 10px;
    }

    .toggle-btn {
        padding: 8px 16px;
        border: 2px solid var(--primary);
        background: transparent;
        color: var(--primary);
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .toggle-btn.active, .toggle-btn:hover {
        background: var(--primary);
        color: white;
    }

    .essay-content {
        background: #f8f9fa;
        border-radius: 16px;
        padding: 25px;
        line-height: 1.8;
        font-size: 1rem;
        border: 1px solid #e9ecef;
    }

    /* Highlighted text styles */
    .highlight-error {
        background: linear-gradient(135deg, #ffe6e6, #ffcccc);
        padding: 2px 4px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s ease;
        border-bottom: 2px solid var(--danger);
    }

    .highlight-error:hover {
        background: linear-gradient(135deg, #ffcccc, #ffb3b3);
        transform: scale(1.02);
    }

    .highlight-error.corrected {
        background: linear-gradient(135deg, rgba(39, 174, 96, 0.2), rgba(46, 204, 113, 0.2));
        border-bottom: 2px solid var(--success);
        color: var(--success);
        font-weight: 600;
    }

    /* Tooltip Styles */
    .correction-tooltip {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.8);
        background: white;
        border-radius: 16px;
        box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        min-width: 400px;
        max-width: 500px;
        z-index: 1500;
        opacity: 0;
        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        pointer-events: none;
    }

    .correction-tooltip.show {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
        pointer-events: all;
    }

    .tooltip-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 1400;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .tooltip-backdrop.show {
        opacity: 1;
    }

    .tooltip-header {
        background: var(--gradient-primary);
        color: white;
        padding: 15px 20px;
        border-radius: 14px 14px 0 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .tooltip-title {
        font-weight: 600;
        font-size: 1rem;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .tooltip-close {
        background: none;
        border: none;
        color: white;
        font-size: 1.2rem;
        cursor: pointer;
        padding: 5px;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .tooltip-close:hover {
        background: rgba(255,255,255,0.2);
    }

    .tooltip-content {
        padding: 20px;
    }

    .tooltip-original, .tooltip-suggested {
        padding: 12px 16px;
        border-radius: 12px;
        margin: 10px 0;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
    }

    .tooltip-original {
        background: linear-gradient(135deg, #ffe6e6, #ffcccc);
        border-left: 4px solid var(--danger);
        color: #c0392b;
    }

    .tooltip-suggested {
        background: linear-gradient(135deg, #e6ffe6, #ccffcc);
        border-left: 4px solid var(--success);
        color: #27ae60;
    }

    .tooltip-explanation {
        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
        padding: 15px;
        border-radius: 12px;
        font-size: 0.9rem;
        color: var(--dark);
        border-left: 4px solid var(--info);
        margin: 15px 0;
    }

    .tooltip-actions {
        padding: 15px 20px;
        border-top: 1px solid #e9ecef;
        display: flex;
        gap: 10px;
        justify-content: center;
    }

    .tooltip-btn {
        padding: 10px 20px;
        border: none;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .btn-accept {
        background: var(--gradient-success);
        color: white;
    }

    .btn-accept:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
    }

    .btn-dismiss {
        background: var(--gradient-secondary);
        color: white;
    }

    .btn-dismiss:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
    }

    /* Criteria Detail Modal */
    .criteria-modal {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.8);
        background: white;
        border-radius: 20px;
        box-shadow: 0 25px 80px rgba(0,0,0,0.3);
        min-width: 500px;
        max-width: 700px;
        max-height: 80vh;
        z-index: 1500;
        opacity: 0;
        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        pointer-events: none;
        overflow: hidden;
    }

    .criteria-modal.show {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
        pointer-events: all;
    }

    .criteria-modal-header {
        background: var(--gradient-primary);
        color: white;
        padding: 20px 25px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
    }

    .criteria-modal-title {
        font-size: 1.3rem;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .criteria-score-badge {
        background: rgba(255,255,255,0.2);
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 1.1rem;
        font-weight: 700;
    }

    .criteria-modal-close {
        background: none;
        border: none;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        padding: 8px;
        border-radius: 50%;
        transition: all 0.3s ease;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .criteria-modal-close:hover {
        background: rgba(255,255,255,0.2);
        transform: rotate(90deg);
    }

    .criteria-modal-body {
        padding: 25px;
        max-height: 60vh;
        overflow-y: auto;
    }

    .criteria-section {
        margin-bottom: 25px;
    }

    .criteria-section-title {
        font-size: 1.1rem;
        font-weight: 700;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
        color: var(--dark);
    }

    .criteria-feedback-box {
        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
        border-left: 4px solid var(--info);
        padding: 18px;
        border-radius: 12px;
        font-size: 1rem;
        line-height: 1.6;
        color: var(--dark);
    }

    .criteria-issues-list, .criteria-improvements-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .criteria-issues-list li {
        background: linear-gradient(135deg, #ffebee, #ffcdd2);
        border-left: 4px solid var(--danger);
        padding: 12px 16px;
        margin-bottom: 8px;
        border-radius: 8px;
        display: flex;
        align-items: flex-start;
        gap: 10px;
    }

    .criteria-issues-list li::before {
        content: "✗";
        color: var(--danger);
        font-weight: bold;
        font-size: 1.1rem;
        margin-top: 2px;
    }

    .criteria-improvements-list li {
        background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
        border-left: 4px solid var(--success);
        padding: 12px 16px;
        margin-bottom: 8px;
        border-radius: 8px;
        display: flex;
        align-items: flex-start;
        gap: 10px;
    }

    .criteria-improvements-list li::before {
        content: "✓";
        color: var(--success);
        font-weight: bold;
        font-size: 1.1rem;
        margin-top: 2px;
    }

    /* Statistics Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 25px;
        margin-top: 30px;
    }

    .stat-card {
        background: white;
        border-radius: 16px;
        padding: 25px;
        text-align: center;
        box-shadow: var(--shadow-soft);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
    }

    .stat-icon {
        font-size: 2.5rem;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 15px;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 800;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 8px;
    }

    .stat-label {
        font-size: 0.9rem;
        color: #6c757d;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    @media (max-width: 768px) {
        .criteria-modal {
            min-width: 90vw;
            max-width: 95vw;
            margin: 20px;
        }

        .criteria-modal-header {
            padding: 15px 20px;
        }

        .criteria-modal-title {
            font-size: 1.1rem;
        }

        .criteria-modal-body {
            padding: 20px;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .stat-card {
            padding: 20px;
        }

        .stat-icon {
            font-size: 2rem;
        }

        .stat-number {
            font-size: 2rem;
        }
    }

    /* Corrections Panel */
    .correction-item {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 12px;
        padding: 15px;
        margin-bottom: 15px;
        border-left: 4px solid var(--danger);
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .correction-item:hover {
        transform: translateX(5px);
        box-shadow: var(--shadow-soft);
    }

    .correction-original {
        font-family: 'Courier New', monospace;
        background: linear-gradient(135deg, #ffe6e6, #ffcccc);
        padding: 8px 12px;
        border-radius: 8px;
        color: var(--danger);
        font-weight: 600;
        margin-bottom: 8px;
    }

    .correction-suggested {
        font-family: 'Courier New', monospace;
        background: linear-gradient(135deg, #e6ffe6, #ccffcc);
        padding: 8px 12px;
        border-radius: 8px;
        color: var(--success);
        font-weight: 600;
        margin-bottom: 8px;
    }

    .correction-explanation {
        font-size: 0.9rem;
        color: #6c757d;
        font-style: italic;
    }

    /* Statistics Overview */
    .stats-overview {
        background: white;
        border-radius: 24px;
        padding: 35px;
        box-shadow: var(--shadow-soft);
        margin-bottom: 50px;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 25px;
    }



    .stat-number {
        font-size: 2rem;
        font-weight: 800;
        color: var(--primary);
        margin-bottom: 8px;
    }

    .stat-label {
        font-size: 0.9rem;
        color: #6c757d;
        font-weight: 500;
    }

    /* Criteria Analysis Table */
    .criteria-analysis-table {
        background: white;
        border-radius: 24px;
        padding: 35px;
        box-shadow: var(--shadow-soft);
        margin-bottom: 50px;
    }

    .analysis-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--dark);
        margin-bottom: 25px;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .criteria-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
    }

    .criteria-table th {
        background: var(--gradient-primary);
        color: white;
        padding: 15px;
        text-align: left;
        font-weight: 600;
        border-radius: 8px 8px 0 0;
    }

    .criteria-table td {
        padding: 15px;
        border-bottom: 1px solid #e9ecef;
        vertical-align: top;
    }

    .criteria-table tr:hover {
        background: #f8f9fa;
    }

    .criteria-name-cell strong {
        color: var(--dark);
        font-weight: 600;
    }

    .score-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .score-pass {
        background: var(--gradient-success);
        color: white;
    }

    .score-warning {
        background: var(--gradient-warning);
        color: white;
    }

    .score-fail {
        background: var(--gradient-danger);
        color: white;
    }

    .score-neutral {
        background: #6c757d;
        color: white;
    }

    /* Share Section */
    .share-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 25px;
        padding: 40px;
        margin: 40px 0;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .share-section::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
        pointer-events: none;
    }

    .share-header {
        text-align: center;
        margin-bottom: 30px;
        position: relative;
        z-index: 2;
    }

    .share-title {
        font-size: 2.2rem;
        font-weight: 800;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;
    }

    .share-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 0;
    }

    .share-content {
        position: relative;
        z-index: 2;
    }

    .share-preview {
        background: rgba(255, 255, 255, 0.15);
        border-radius: 20px;
        padding: 25px;
        margin-bottom: 30px;
        display: flex;
        align-items: center;
        gap: 25px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .preview-score {
        text-align: center;
        min-width: 100px;
    }

    .preview-number {
        display: block;
        font-size: 3rem;
        font-weight: 800;
        line-height: 1;
        text-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .preview-label {
        display: block;
        font-size: 0.9rem;
        opacity: 0.8;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-top: 5px;
    }

    .preview-info h5 {
        margin: 0 0 5px 0;
        font-weight: 700;
        font-size: 1.3rem;
    }

    .preview-info p {
        margin: 0;
        opacity: 0.8;
    }

    .share-url-container {
        margin-bottom: 25px;
    }

    .share-label {
        display: block;
        font-weight: 600;
        margin-bottom: 10px;
        font-size: 1rem;
    }

    .share-url-group {
        display: flex;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 15px;
        overflow: hidden;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .share-url-input {
        flex: 1;
        background: transparent;
        border: none;
        padding: 15px 20px;
        color: white;
        font-size: 0.95rem;
        outline: none;
    }

    .share-url-input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .share-copy-btn {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        padding: 15px 25px;
        color: white;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .share-copy-btn:hover {
        background: rgba(255, 255, 255, 0.3);
    }

    .share-features {
        display: flex;
        justify-content: center;
        gap: 30px;
        margin-bottom: 25px;
        flex-wrap: wrap;
    }

    .feature-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.95rem;
        opacity: 0.9;
    }

    .feature-item i {
        font-size: 1.1rem;
    }

    .share-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
        flex-wrap: wrap;
    }

    .share-btn {
        padding: 12px 25px;
        border-radius: 12px;
        border: none;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.95rem;
    }

    .share-btn-primary {
        background: white;
        color: var(--primary-color);
    }

    .share-btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(255, 255, 255, 0.3);
    }

    .share-btn-secondary {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .share-btn-secondary:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Responsive Design for Share Section */
    @media (max-width: 768px) {
        .share-section {
            padding: 25px 20px;
            margin: 30px 0;
        }

        .share-title {
            font-size: 1.8rem;
            flex-direction: column;
            gap: 10px;
        }

        .share-preview {
            flex-direction: column;
            text-align: center;
            gap: 15px;
        }

        .preview-number {
            font-size: 2.5rem;
        }

        .share-features {
            flex-direction: column;
            gap: 15px;
            align-items: center;
        }

        .share-buttons {
            flex-direction: column;
            align-items: stretch;
        }

        .share-btn {
            justify-content: center;
        }

        .share-url-group {
            flex-direction: column;
        }

        .share-copy-btn {
            justify-content: center;
            border-radius: 0 0 15px 15px;
        }

        .share-url-input {
            border-radius: 15px 15px 0 0;
        }
    }

    /* Action Buttons */
    .action-buttons {
        text-align: center;
        padding: 40px;
    }

    .btn-primary, .btn-outline-secondary, .btn-warning {
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1rem;
        margin: 0 10px;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background: var(--gradient-primary);
        border: none;
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: var(--shadow-medium);
    }

    .btn-warning {
        background: var(--gradient-warning);
        border: none;
        color: white;
    }

    .btn-warning:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 50px rgba(243, 156, 18, 0.3);
    }

    .btn-warning:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    .btn-outline-secondary {
        border: 2px solid #6c757d;
        color: #6c757d;
        background: transparent;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-3px);
    }

    /* Question Display Styles */
    .question-display {
        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
        border-radius: 16px;
        padding: 25px;
        margin-bottom: 25px;
        border-left: 5px solid var(--info);
        box-shadow: var(--shadow-soft);
        transition: all 0.3s ease;
    }

    .question-display:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
    }

    .question-header {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
        color: var(--dark);
        font-size: 1.1rem;
        font-weight: 700;
    }

    .question-header i {
        color: var(--info);
        font-size: 1.2rem;
    }

    .question-content {
        background: rgba(255, 255, 255, 0.8);
        padding: 18px;
        border-radius: 12px;
        font-size: 1.05rem;
        line-height: 1.6;
        color: var(--dark);
        margin-bottom: 15px;
        border: 1px solid rgba(255, 255, 255, 0.5);
    }

    .question-meta {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
    }

    .task-type-badge, .time-limit-badge, .word-count-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .task-type-badge {
        background: var(--gradient-primary);
        color: white;
    }

    .time-limit-badge {
        background: var(--gradient-warning);
        color: white;
    }

    .word-count-badge {
        background: var(--gradient-success);
        color: white;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .header-title { font-size: 2.5rem; }
        .content-section { padding: 30px 20px; }
        .score-dashboard { grid-template-columns: 1fr; }
        .analysis-section { grid-template-columns: 1fr; }
        .criteria-grid { grid-template-columns: 1fr; }
        .stats-grid { grid-template-columns: repeat(2, 1fr); }

        .question-display {
            padding: 20px;
        }

        .question-meta {
            justify-content: center;
        }

        .task-type-badge, .time-limit-badge, .word-count-badge {
            font-size: 0.75rem;
            padding: 5px 10px;
        }
    }
</style>

<div class="container-fluid">
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <div class="header-content">
                <h1 class="header-title animate__animated animate__fadeInDown">
                    <i class="fas fa-chart-line"></i>
                    Essay Analysis Results
                </h1>
                <p class="header-subtitle animate__animated animate__fadeInUp animate__delay-1s">
                    Comprehensive AI-Powered Assessment with Detailed Feedback & Scoring
                </p>
            </div>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            @if($attempt->status === 'failed')
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Scoring Error:</strong> {{ $attempt->error_message }}
                </div>
            @elseif($attempt->status === 'pending')
                <div class="alert alert-warning">
                    <i class="fas fa-clock me-2"></i>
                    <strong>Processing:</strong> Your essay is being analyzed by AI. Please wait a moment.
                </div>
            @endif

            @if($attempt->isCompleted())
            <!-- Score Dashboard -->
            <div class="score-dashboard animate__animated animate__fadeInUp">
                <div class="overall-score-card">
                    <div class="score-number">{{ $attempt->overall_band_score }}</div>
                    <div class="score-label">Overall Band Score</div>
                    <div class="score-description">{{ $attempt->getBandDescription() }}</div>
                </div>

                <div class="criteria-breakdown">
                    <h3 class="criteria-title">
                        <i class="fas fa-chart-bar"></i>
                        Criteria Breakdown
                    </h3>
                    <div class="criteria-grid" id="criteriaGrid">
                        <div class="criteria-item">
                            <div class="criteria-name">Task Achievement</div>
                            <div class="criteria-score">{{ $attempt->task_achievement ?? '-' }}</div>
                            <div class="criteria-feedback">Phân tích đề bài và triển khai ý</div>
                            <div class="criteria-action">
                                <i class="fas fa-eye"></i> Xem chi tiết
                            </div>
                        </div>
                        <div class="criteria-item">
                            <div class="criteria-name">Coherence & Cohesion</div>
                            <div class="criteria-score">{{ $attempt->coherence_cohesion ?? '-' }}</div>
                            <div class="criteria-feedback">Tổ chức bài và liên kết ý</div>
                            <div class="criteria-action">
                                <i class="fas fa-eye"></i> Xem chi tiết
                            </div>
                        </div>
                        <div class="criteria-item">
                            <div class="criteria-name">Lexical Resource</div>
                            <div class="criteria-score">{{ $attempt->lexical_resource ?? '-' }}</div>
                            <div class="criteria-feedback">Từ vựng và cách dùng từ</div>
                            <div class="criteria-action">
                                <i class="fas fa-eye"></i> Xem chi tiết
                            </div>
                        </div>
                        <div class="criteria-item">
                            <div class="criteria-name">Grammar Range & Accuracy</div>
                            <div class="criteria-score">{{ $attempt->grammar_accuracy ?? '-' }}</div>
                            <div class="criteria-feedback">Ngữ pháp và độ chính xác</div>
                            <div class="criteria-action">
                                <i class="fas fa-eye"></i> Xem chi tiết
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analysis Section -->
            <div class="analysis-section animate__animated animate__fadeInUp animate__delay-1s">
                <div class="essay-analysis">
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-file-text"></i>
                            Your Essay Analysis
                        </h3>
                        <div class="view-toggle">
                            <button class="toggle-btn active" onclick="toggleView('highlighted')">With Corrections</button>
                            <button class="toggle-btn" onclick="toggleView('original')">Original</button>
                        </div>
                    </div>

                    <div class="essay-content" id="essayContent">
                        @php
                            $questionText = $attempt->getQuestionText();
                        @endphp
                        @if($questionText)
                            <div class="question-display">
                                <div class="question-header">
                                    <i class="fas fa-question-circle"></i>
                                    <strong>Essay Question</strong>
                                </div>
                                <div class="question-content">
                                    {{ $questionText }}
                                </div>
                                <div class="question-meta">
                                    <span class="task-type-badge">{{ strtoupper(str_replace('_', ' ', $attempt->task_type)) }}</span>
                                    <span class="time-limit-badge">{{ $attempt->time_limit ?? 40 }} minutes</span>
                                    <span class="word-count-badge">{{ $attempt->word_count }} words</span>
                                </div>
                            </div>
                        @endif

                        <div id="highlightedEssay">
                            {!! nl2br(e($attempt->essay_content)) !!}
                        </div>

                        <div id="originalEssay" style="display: none;">
                            {!! nl2br(e($attempt->essay_content)) !!}
                        </div>
                    </div>
                </div>

                <div class="corrections-panel">
                    <h3 class="section-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        Key Corrections
                        <span class="badge bg-danger ms-2">{{ is_array($attempt->highlighted_corrections) ? count($attempt->highlighted_corrections) : 0 }}</span>
                    </h3>
                    <div id="correctionsContainer">
                        @if($attempt->highlighted_corrections && count($attempt->highlighted_corrections) > 0)
                            @foreach($attempt->highlighted_corrections as $index => $correction)
                            <div class="correction-item" onclick="highlightCorrection({{ $index }})">
                                <div class="correction-original">{{ $correction['original_text'] ?? '' }}</div>
                                <div class="correction-suggested">{{ $correction['suggested_correction'] ?? '' }}</div>
                                <div class="correction-explanation">{{ $correction['explanation'] ?? '' }}</div>
                            </div>
                            @endforeach
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                <h5>Great job!</h5>
                                <p class="text-muted">No major corrections needed.</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
            <!-- Statistics Overview -->
            <div class="stats-overview animate__animated animate__fadeInUp animate__delay-2s">
                <h3 class="analysis-title">
                    <i class="fas fa-chart-pie"></i>
                    Essay Statistics
                </h3>
                <div class="stats-grid">
                    <div class="stat-card animate__animated animate__fadeInUp">
                        <div class="stat-icon">
                            <i class="fas fa-file-word"></i>
                        </div>
                        <div class="stat-number">{{ $attempt->word_count ?? 0 }}</div>
                        <div class="stat-label">Total Words</div>
                    </div>
                    <div class="stat-card animate__animated animate__fadeInUp animate__delay-1s">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="stat-number">{{ $attempt->statistics['total_errors'] ?? 0 }}</div>
                        <div class="stat-label">Total Errors</div>
                    </div>
                    <div class="stat-card animate__animated animate__fadeInUp animate__delay-2s">
                        <div class="stat-icon">
                            <i class="fas fa-spell-check"></i>
                        </div>
                        <div class="stat-number">{{ $attempt->statistics['grammar_errors'] ?? 0 }}</div>
                        <div class="stat-label">Grammar Errors</div>
                    </div>
                    <div class="stat-card animate__animated animate__fadeInUp animate__delay-3s">
                        <div class="stat-icon">
                            <i class="fas fa-font"></i>
                        </div>
                        <div class="stat-number">{{ $attempt->statistics['spelling_errors'] ?? 0 }}</div>
                        <div class="stat-label">Spelling Errors</div>
                    </div>
                    <div class="stat-card animate__animated animate__fadeInUp animate__delay-4s">
                        <div class="stat-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="stat-number">{{ $attempt->statistics['vocabulary_errors'] ?? 0 }}</div>
                        <div class="stat-label">Vocabulary Issues</div>
                    </div>
                    <div class="stat-card animate__animated animate__fadeInUp animate__delay-5s">
                        <div class="stat-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="stat-number">{{ $attempt->getCefrLevel() }}</div>
                        <div class="stat-label">CEFR Level</div>
                    </div>
                </div>
            </div>

            <!-- Detailed Criteria Analysis -->
            @if($attempt->detailed_feedback)
            <div class="criteria-analysis-table animate__animated animate__fadeInUp animate__delay-3s">
                <h3 class="analysis-title">
                    <i class="fas fa-microscope"></i>
                    Phân Tích Chi Tiết Theo Tiêu Chí
                </h3>
                <table class="criteria-table">
                    <thead>
                        <tr>
                            <th>Tiêu Chí</th>
                            <th>Điểm</th>
                            <th>Nhận Xét</th>
                            <th>Vấn Đề Chính</th>
                            <th>Cải Thiện</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($attempt->detailed_feedback as $criterion => $feedback)
                        <tr>
                            <td class="criteria-name-cell">
                                @php
                                    $criteriaNames = [
                                        'task_achievement' => 'Hoàn thành nhiệm vụ',
                                        'coherence_cohesion' => 'Tính mạch lạc và liên kết',
                                        'lexical_resource' => 'Từ vựng',
                                        'grammar_accuracy' => 'Ngữ pháp và độ chính xác'
                                    ];
                                @endphp
                                <strong>{{ $criteriaNames[$criterion] ?? ucfirst(str_replace('_', ' ', $criterion)) }}</strong>
                            </td>
                            <td>
                                @php
                                    $score = $feedback['score'] ?? 0;
                                    $badgeClass = $score >= 7 ? 'score-pass' : ($score >= 5 ? 'score-warning' : 'score-fail');
                                @endphp
                                <span class="score-badge {{ $badgeClass }}">{{ $score }}</span>
                            </td>
                            <td>{{ $feedback['feedback'] ?? 'Chưa có nhận xét' }}</td>
                            <td>
                                @if(isset($feedback['issues']) && count($feedback['issues']) > 0)
                                    <ul class="mb-0">
                                        @foreach(array_slice($feedback['issues'], 0, 3) as $issue)
                                            <li>{{ $issue }}</li>
                                        @endforeach
                                    </ul>
                                @else
                                    <span class="text-muted">Không có vấn đề</span>
                                @endif
                            </td>
                            <td>
                                @if(isset($feedback['improvements']) && count($feedback['improvements']) > 0)
                                    <ul class="mb-0">
                                        @foreach(array_slice($feedback['improvements'], 0, 3) as $improvement)
                                            <li>{{ $improvement }}</li>
                                        @endforeach
                                    </ul>
                                @else
                                    <span class="text-muted">Tiếp tục phát huy!</span>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            @endif
            @endif

            <!-- Share Section -->
            @if($attempt->canBeShared())
            <div class="share-section animate__animated animate__fadeInUp" data-aos="fade-up" data-aos-delay="600">
                <div class="share-header">
                    <h3 class="share-title">
                        <i class="fas fa-share-alt"></i>
                        Share Your Achievement
                    </h3>
                    <p class="share-subtitle">
                        Share your IELTS score with friends, family, or employers
                    </p>
                </div>

                <div class="share-content">
                    <div class="share-preview">
                        <div class="preview-score">
                            <span class="preview-number">{{ number_format($attempt->overall_band_score, 1) }}</span>
                            <span class="preview-label">Band Score</span>
                        </div>
                        <div class="preview-info">
                            <h5>{{ $attempt->getBandDescription() }}</h5>
                            <p class="text-muted">CEFR Level: {{ $attempt->getCefrLevel() }}</p>
                        </div>
                    </div>

                    <div class="share-actions">
                        <div class="share-url-container">
                            <label class="share-label">Public Share Link:</label>
                            <div class="share-url-group">
                                <input type="text" class="share-url-input" id="shareUrlMain" value="{{ $attempt->getShareUrl() }}" readonly>
                                <button class="share-copy-btn" onclick="copyMainShareUrl()">
                                    <i class="fas fa-copy"></i>
                                    <span class="copy-text">Copy</span>
                                </button>
                            </div>
                        </div>

                        <div class="share-features">
                            <div class="feature-item">
                                <i class="fas fa-eye"></i>
                                <span>No login required</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-shield-alt"></i>
                                <span>Secure encrypted link</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-chart-bar"></i>
                                <span>Full score breakdown</span>
                            </div>
                        </div>

                        <div class="share-buttons">

                            <button class="share-btn share-btn-secondary" onclick="shareViaEmail()">
                                <i class="fas fa-envelope"></i>
                                Email
                            </button>
                            <button class="share-btn share-btn-secondary" onclick="shareViaSocial()">
                                <i class="fas fa-share-nodes"></i>
                                Social
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Action Buttons -->
            <div class="action-buttons">
                @if(!$isPublicView)
                    @if($attempt->isCompleted())
                    <button onclick="rescoreEssay()" class="btn btn-warning" id="rescoreBtn">
                        <i class="fas fa-redo"></i> Chấm lại điểm (Trừ 1 credit)
                    </button>
                    @endif
                    <a href="{{ route('scoring.create') }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Score Another Essay
                    </a>
                    <a href="{{ route('scoring.history') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-history"></i> View History
                    </a>
                    <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-chart-line"></i> Dashboard
                    </a>
                @else
                    <div class="public-view-actions">
                        <a href="{{ route('home') }}" class="btn btn-primary">
                            <i class="fas fa-home"></i> Trang chủ
                        </a>
                        <a href="{{ route('register') }}" class="btn btn-success">
                            <i class="fas fa-user-plus"></i> Đăng ký để chấm điểm
                        </a>
                        <a href="{{ route('login') }}" class="btn btn-outline-primary">
                            <i class="fas fa-sign-in-alt"></i> Đăng nhập
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Global variables
let currentEssayData = @json($attempt);
let activeCorrection = -1;
let activeTooltip = null;
let correctedTexts = new Set();

// Initialize application
$(document).ready(function() {
    initializeInteractions();

    // Default to highlighted view if corrections exist
    if (currentEssayData.highlighted_corrections && currentEssayData.highlighted_corrections.length > 0) {
        processHighlightedText();
        // Ensure highlighted view is shown by default
        $('#highlightedEssay').show();
        $('#originalEssay').hide();
    } else {
        // If no corrections, show original and switch active tab
        $('#originalEssay').show();
        $('#highlightedEssay').hide();
        $('.toggle-btn').removeClass('active');
        $('button[onclick="toggleView(\'original\')"]').addClass('active');
    }

    // Close tooltip when clicking outside
    $(document).on('click', function(event) {
        if (activeTooltip && !$(event.target).closest('.correction-tooltip').length && !$(event.target).closest('.highlight-error').length) {
            hideCorrectionTooltip();
        }
    });
});

// Initialize interactive elements
function initializeInteractions() {
    // Criteria item click handlers
    $('.criteria-item').on('click', function() {
        const criteriaName = $(this).find('.criteria-name').text().trim();
        showCriteriaDetails(criteriaName);
    });

    // Also handle "Xem chi tiết" button clicks
    $('.criteria-item button').on('click', function(e) {
        e.stopPropagation();
        const criteriaName = $(this).closest('.criteria-item').find('.criteria-name').text().trim();
        showCriteriaDetails(criteriaName);
    });
}

// Toggle between original and highlighted essay view
function toggleView(viewType) {
    $('.toggle-btn').removeClass('active');
    $(`button[onclick="toggleView('${viewType}')"]`).addClass('active');

    if (viewType === 'original') {
        $('#originalEssay').show();
        $('#highlightedEssay').hide();
    } else if (viewType === 'highlighted') {
        $('#originalEssay').hide();
        $('#highlightedEssay').show();
    }
}

// Process highlighted text with corrections
function processHighlightedText() {
    const corrections = currentEssayData.highlighted_corrections;
    if (!corrections || corrections.length === 0) return;

    let highlightedContent = currentEssayData.essay_content;

    // Apply highlights to text
    corrections.forEach((correction, index) => {
        if (correction.original_text) {
            const isAccepted = correctedTexts.has(index);
            const displayText = isAccepted ? correction.suggested_correction : correction.original_text;
            const cssClass = isAccepted ? 'highlight-error corrected' : 'highlight-error';

            const highlightedText = `<span class="${cssClass}" data-correction="${index}" onclick="showCorrectionTooltip(event, ${index})">${displayText}</span>`;
            highlightedContent = highlightedContent.replace(correction.original_text, highlightedText);
        }
    });

    $('#highlightedEssay').html(highlightedContent.replace(/\n/g, '<br>'));
}

// Highlight specific correction
function highlightCorrection(index) {
    // Remove previous highlights
    $('.correction-item').removeClass('active');
    $('.highlight-error').removeClass('active');

    // Add new highlights
    $(`.correction-item:eq(${index})`).addClass('active');
    $(`.highlight-error[data-correction="${index}"]`).addClass('active');

    // Scroll to correction
    const correctionElement = $(`.correction-item:eq(${index})`);
    if (correctionElement.length) {
        correctionElement[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    activeCorrection = index;
}

// Show correction tooltip
function showCorrectionTooltip(event, index) {
    event.stopPropagation();

    if (correctedTexts.has(index)) return; // Don't show tooltip for already corrected text

    // If tooltip is already showing for this correction, hide it
    if (activeTooltip && activeTooltip.dataset.correctionIndex == index) {
        hideCorrectionTooltip();
        return;
    }

    // Hide any existing tooltip
    hideCorrectionTooltip();

    const corrections = currentEssayData.highlighted_corrections;
    const correction = corrections[index];
    if (!correction) return;

    // Create tooltip
    const tooltip = document.createElement('div');
    tooltip.className = 'correction-tooltip';
    tooltip.dataset.correctionIndex = index;
    tooltip.innerHTML = `
        <div class="tooltip-header">
            <div class="tooltip-title">
                <i class="fas fa-lightbulb"></i>
                Correction Suggestion
            </div>
            <button class="tooltip-close" onclick="hideCorrectionTooltip()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="tooltip-content">
            <div class="tooltip-original">
                <strong><i class="fas fa-times text-danger"></i> Original:</strong><br>
                "${correction.original_text}"
            </div>
            <div class="tooltip-suggested">
                <strong><i class="fas fa-check text-success"></i> Suggested:</strong><br>
                "${correction.suggested_correction}"
            </div>
            <div class="tooltip-explanation">
                <i class="fas fa-info-circle"></i> ${correction.explanation || 'Sai chính tả hoặc ngữ pháp'}
            </div>
        </div>
        <div class="tooltip-actions">
            <button class="tooltip-btn btn-accept" onclick="acceptCorrection(${index})">
                <i class="fas fa-check"></i> Accept
            </button>
            <button class="tooltip-btn btn-dismiss" onclick="hideCorrectionTooltip()">
                <i class="fas fa-times"></i> Dismiss
            </button>
        </div>
    `;

    // Create backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'tooltip-backdrop';
    backdrop.onclick = hideCorrectionTooltip;

    // Add to document
    document.body.appendChild(backdrop);
    document.body.appendChild(tooltip);
    activeTooltip = tooltip;

    // Show tooltip with animation
    setTimeout(() => {
        backdrop.classList.add('show');
        tooltip.classList.add('show');
    }, 10);
}

function hideCorrectionTooltip() {
    if (activeTooltip) {
        const backdrop = document.querySelector('.tooltip-backdrop');

        // Hide with animation
        activeTooltip.classList.remove('show');
        if (backdrop) {
            backdrop.classList.remove('show');
        }

        setTimeout(() => {
            // Remove tooltip
            if (activeTooltip && activeTooltip.parentNode) {
                activeTooltip.parentNode.removeChild(activeTooltip);
            }

            // Remove backdrop
            if (backdrop && backdrop.parentNode) {
                backdrop.parentNode.removeChild(backdrop);
            }

            activeTooltip = null;
        }, 300);
    }
}

function acceptCorrection(index) {
    correctedTexts.add(index);
    hideCorrectionTooltip();

    // Update the essay display
    processHighlightedText();
}

// Show criteria details modal
function showCriteriaDetails(criteriaName) {
    const feedback = currentEssayData.detailed_feedback;
    if (!feedback) return;

    // Map criteria names to keys
    const criteriaMapping = {
        'Task Achievement': 'task_achievement',
        'Coherence & Cohesion': 'coherence_cohesion',
        'Lexical Resource': 'lexical_resource',
        'Grammar Range & Accuracy': 'grammar_accuracy'
    };

    const criteriaKey = criteriaMapping[criteriaName] || criteriaName.toLowerCase().replace(/\s+/g, '_').replace('&', '');
    const criteriaData = feedback[criteriaKey];

    if (!criteriaData) return;

    // Create modal
    const modal = document.createElement('div');
    modal.className = 'criteria-modal';
    modal.innerHTML = `
        <div class="criteria-modal-header">
            <div class="criteria-modal-title">
                <i class="fas fa-chart-line"></i>
                ${criteriaName}
            </div>
            <div class="criteria-score-badge">
                Điểm: ${criteriaData.score || 'N/A'}
            </div>
            <button class="criteria-modal-close" onclick="closeCriteriaModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="criteria-modal-body">
            <div class="criteria-section">
                <div class="criteria-section-title">
                    <i class="fas fa-comment-dots"></i>
                    Nhận Xét Tổng Quan
                </div>
                <div class="criteria-feedback-box">
                    ${criteriaData.feedback || 'Chưa có nhận xét chi tiết'}
                </div>
            </div>

            ${criteriaData.issues && criteriaData.issues.length > 0 ? `
                <div class="criteria-section">
                    <div class="criteria-section-title">
                        <i class="fas fa-exclamation-triangle" style="color: var(--danger);"></i>
                        Các Vấn Đề Cần Cải Thiện
                    </div>
                    <ul class="criteria-issues-list">
                        ${criteriaData.issues.map(issue => `<li>${issue}</li>`).join('')}
                    </ul>
                </div>
            ` : ''}

            ${criteriaData.improvements && criteriaData.improvements.length > 0 ? `
                <div class="criteria-section">
                    <div class="criteria-section-title">
                        <i class="fas fa-lightbulb" style="color: var(--success);"></i>
                        Hướng Dẫn Cải Thiện
                    </div>
                    <ul class="criteria-improvements-list">
                        ${criteriaData.improvements.map(improvement => `<li>${improvement}</li>`).join('')}
                    </ul>
                </div>
            ` : ''}
        </div>
    `;

    // Create backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'tooltip-backdrop';
    backdrop.onclick = closeCriteriaModal;

    // Add to document
    document.body.appendChild(backdrop);
    document.body.appendChild(modal);

    // Show modal with animation
    setTimeout(() => {
        backdrop.classList.add('show');
        modal.classList.add('show');
    }, 10);

    // Store reference for closing
    window.activeCriteriaModal = modal;
}

function closeCriteriaModal() {
    const modal = window.activeCriteriaModal;
    if (modal) {
        const backdrop = document.querySelector('.tooltip-backdrop');

        // Hide with animation
        modal.classList.remove('show');
        if (backdrop) {
            backdrop.classList.remove('show');
        }

        setTimeout(() => {
            // Remove modal
            if (modal && modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }

            // Remove backdrop
            if (backdrop && backdrop.parentNode) {
                backdrop.parentNode.removeChild(backdrop);
            }

            window.activeCriteriaModal = null;
        }, 300);
    }
}

// Add CSS for active states
$('head').append(`
<style>
.correction-item.active {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb) !important;
    border-left-color: var(--info) !important;
    transform: translateX(10px);
}

.highlight-error.active {
    background: linear-gradient(135deg, #ffeb3b, #ffc107) !important;
    border-bottom-color: var(--warning) !important;
    transform: scale(1.05);
}

.criteria-item:hover {
    cursor: pointer;
}

.animate__animated {
    animation-duration: 0.8s;
}

.animate__delay-1s {
    animation-delay: 0.3s;
}

.animate__delay-2s {
    animation-delay: 0.6s;
}

.animate__delay-3s {
    animation-delay: 0.9s;
}
</style>
`);

// Share functionality
function copyMainShareUrl() {
    const shareUrl = document.getElementById('shareUrlMain');
    shareUrl.select();
    shareUrl.setSelectionRange(0, 99999);

    navigator.clipboard.writeText(shareUrl.value).then(function() {
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        const copyText = btn.querySelector('.copy-text');

        btn.innerHTML = '<i class="fas fa-check"></i><span class="copy-text">Copied!</span>';
        btn.style.background = 'rgba(40, 167, 69, 0.3)';

        setTimeout(function() {
            btn.innerHTML = originalText;
            btn.style.background = 'rgba(255, 255, 255, 0.2)';
        }, 2000);
    }).catch(function(err) {
        // Fallback for older browsers
        shareUrl.select();
        document.execCommand('copy');
        alert('Share URL copied to clipboard!');
    });
}

function shareViaEmail() {
    const shareUrl = document.getElementById('shareUrlMain').value;
    const subject = encodeURIComponent('Check out my IELTS Score!');
    const body = encodeURIComponent(`I just got my IELTS Writing score and wanted to share it with you!\n\nMy Band Score: {{ $attempt->overall_band_score }}\nLevel: {{ $attempt->getBandDescription() }}\n\nView my detailed results here: ${shareUrl}\n\nScored using IELTS AI - the smart way to improve your writing!`);

    window.open(`mailto:?subject=${subject}&body=${body}`, '_blank');
}

function shareViaSocial() {
    const shareUrl = document.getElementById('shareUrlMain').value;
    const text = encodeURIComponent(`Just scored {{ $attempt->overall_band_score }} on my IELTS Writing test! 🎉 Check out my detailed results:`);

    // Create a simple modal with social options
    const socialModal = `
        <div class="modal fade" id="socialShareModal" tabindex="-1">
            <div class="modal-dialog modal-sm">
                <div class="modal-content" style="border-radius: 15px;">
                    <div class="modal-header bg-primary text-white" style="border-radius: 15px 15px 0 0;">
                        <h6 class="modal-title">Share on Social Media</h6>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <div class="d-grid gap-2">
                            <a href="https://twitter.com/intent/tweet?text=${text}&url=${encodeURIComponent(shareUrl)}" target="_blank" class="btn btn-info">
                                <i class="fab fa-twitter"></i> Twitter
                            </a>
                            <a href="https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}" target="_blank" class="btn btn-primary">
                                <i class="fab fa-facebook"></i> Facebook
                            </a>
                            <a href="https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}" target="_blank" class="btn btn-secondary">
                                <i class="fab fa-linkedin"></i> LinkedIn
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('socialShareModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add new modal
    document.body.insertAdjacentHTML('beforeend', socialModal);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('socialShareModal'));
    modal.show();
}

// Rescore essay function
function rescoreEssay() {
    // Show confirmation dialog
    if (!confirm('Bạn có chắc chắn muốn chấm lại điểm? Điều này sẽ trừ 1 credit từ tài khoản của bạn.')) {
        return;
    }

    const rescoreBtn = document.getElementById('rescoreBtn');
    const originalText = rescoreBtn.innerHTML;

    // Disable button and show loading
    rescoreBtn.disabled = true;
    rescoreBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang chấm lại...';

    // Make AJAX request to rescore
    fetch('{{ route("scoring.rescore", $attempt->getHashid()) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message and reload page
            alert('Chấm điểm thành công! Trang sẽ được tải lại để hiển thị kết quả mới.');
            window.location.reload();
        } else {
            // Show error message
            alert('Lỗi: ' + (data.message || 'Không thể chấm lại điểm. Vui lòng thử lại sau.'));

            // Re-enable button
            rescoreBtn.disabled = false;
            rescoreBtn.innerHTML = originalText;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra. Vui lòng thử lại sau.');

        // Re-enable button
        rescoreBtn.disabled = false;
        rescoreBtn.innerHTML = originalText;
    });
}
</script>
@endpush



@endsection
